<?php

namespace Nzoom\Export\Provider;

use Nzoom\Export\Entity\ExportTable;
use Nzoom\Export\Entity\ExportTableCollection;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;

/**
 * Class ModelTableProvider
 *
 * Automatically discovers and extracts table data from Model objects
 * Looks for variables with 'type' => 'grouping' to identify tables
 */
class ModelTableProvider implements ExportTableProviderInterface
{
    /**
     * @var \Registry
     */
    private $registry;

    /**
     * @var array Cache of discovered table headers by table type
     */
    private $tableHeaders = [];

    /**
     * @var array Configuration options
     */
    private $options;

    /**
     * ModelTableProvider constructor
     *
     * @param \Registry $registry
     * @param array $options Optional configuration (max_records_per_table, include_empty_tables, etc.)
     */
    public function __construct(\Registry $registry, array $options = [])
    {
        $this->registry = $registry;
        $this->options = array_merge($this->getDefaultOptions(), $options);
    }

    /**
     * Get default options
     *
     * @return array
     */
    private function getDefaultOptions(): array
    {
        return [
            'include_empty_tables' => false,
            'date_format' => 'd.m.Y',
            'datetime_format' => 'd.m.Y H:i',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getTablesForRecord($record, array $options = []): ExportTableCollection
    {
        $collection = new ExportTableCollection();
        $mergedOptions = array_merge($this->options, $options);

        if (!($record instanceof \Model)) {
            return $collection;
        }

        try {
            // Discover all grouping variables in the model
            $groupingVars = $this->discoverGroupingVariables($record);

            foreach ($groupingVars as $varName => $groupingData) {
                $table = $this->createTableFromGroupingData($record, $varName, $groupingData, $mergedOptions);

                if ($table && ($table->hasRecords() || $mergedOptions['include_empty_tables'])) {
                    $collection->addTable($table);
                }
            }
        } catch (\Exception $e) {
            // Log error but continue
            if (isset($this->registry['logger'])) {
                $this->registry['logger']->warning(
                    "Failed to extract tables from model: " . $e->getMessage()
                );
            }
        }

        return $collection;
    }

    /**
     * Discover all grouping variables in a model
     *
     * @param \Model $model
     * @return array Array of grouping variables with their data
     */
    private function discoverGroupingVariables(\Model $model): array
    {
        $groupingVars = [];

        try {
            // Get all variables from the model using the proper method
            // This will get the variables in the format needed for templates/exports
            if (!$model->checkForVariables()) {
                return $groupingVars;
            }

            // Ensure model is unsanitized to access variables
            $wasSanitized = $model->isSanitized();
            if ($wasSanitized) {
                $model->unsanitize();
            }

            $modelVars = $model->getVarsForTemplateAssoc();

            // Restore sanitization state
            if ($wasSanitized) {
                $model->sanitize();
            }

            // Check each variable to see if it has 'type' => 'group'
            foreach ($modelVars??[] as $varName => $varData) {
                if (is_array($varData) && isset($varData['type']) && $varData['type'] === 'group') {
                    $groupingVars[$varName] = $varData;
                }
            }
        } catch (\Exception $e) {
            // Log error but continue
            if (isset($this->registry['logger'])) {
                $this->registry['logger']->warning(
                    "Failed to discover grouping variables from model: " . $e->getMessage()
                );
            }
        }

        return $groupingVars;
    }

    /**
     * Create a table from grouping data
     *
     * @param \Model $model
     * @param string $varName
     * @param array $groupingData
     * @param array $options
     * @return ExportTable|null
     */
    private function createTableFromGroupingData(\Model $model, string $varName, array $groupingData, array $options): ?ExportTable
    {
        // Extract table structure from grouping data
        $names = $groupingData['names'] ?? [];
        $labels = $groupingData['labels'] ?? [];
        $hidden = $groupingData['hidden'] ?? [];
        $values = $groupingData['values'] ?? [];

        if (empty($names) || empty($labels)) {
            return null;
        }

        // Get or create shared header for this table type
        $header = $this->getOrCreateTableHeader($varName, $names, $labels, $hidden);

        // Create table name from variable name
        $tableName = $this->formatTableName($varName);

        // Create table
        $table = new ExportTable(
            $varName,
            $tableName,
            $header,
            $model->get('id'),
            ['source_model' => get_class($model)]
        );

        // Populate table with data
        $this->populateTableFromGroupingData($table, $values, $names, $hidden, $options);

        return $table;
    }

    /**
     * Get or create a shared table header for a table type
     *
     * @param string $tableType
     * @param array $names
     * @param array $labels
     * @param array $hidden
     * @return ExportHeader
     */
    private function getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden): ExportHeader
    {
        // Check if we already have a header for this table type
        if (isset($this->tableHeaders[$tableType])) {
            return $this->tableHeaders[$tableType];
        }

        // Create new header
        $header = new ExportHeader();

        foreach ($names as $index => $varName) {
            // Skip hidden columns
            if (in_array($varName, $hidden) || in_array($index, $hidden)) {
                continue;
            }

            $label = $labels[$index] ?? $varName;
            $type = $this->guessColumnType($varName);

            $column = new ExportColumn($varName, $label, $type);
            $header->addColumn($column);
        }

        // Cache the header for reuse
        $this->tableHeaders[$tableType] = $header;

        return $header;
    }

    /**
     * Format table name from variable name
     *
     * @param string $varName
     * @return string
     */
    private function formatTableName(string $varName): string
    {
        // Convert snake_case or camelCase to Title Case
        $name = str_replace(['_', '-'], ' ', $varName);
        $name = ucwords($name);

        return $name;
    }

    /**
     * Guess column type from variable name
     *
     * @param string $varName
     * @return string
     */
    private function guessColumnType(string $varName): string
    {
        $varName = strtolower($varName);

        // Date/datetime patterns
        if (preg_match('/(date|time|created|updated|modified)/', $varName)) {
            if (preg_match('/(datetime|timestamp|created_at|updated_at)/', $varName)) {
                return ExportValue::TYPE_DATETIME;
            }
            return ExportValue::TYPE_DATE;
        }

        // Numeric patterns
        if (preg_match('/(id|count|quantity|amount|price|total|sum|number)/', $varName)) {
            if (preg_match('/(price|amount|total|sum|rate|cost)/', $varName)) {
                return ExportValue::TYPE_FLOAT;
            }
            return ExportValue::TYPE_INTEGER;
        }

        // Boolean patterns
        if (preg_match('/(is_|has_|can_|active|enabled|disabled|visible|hidden)/', $varName)) {
            return ExportValue::TYPE_BOOLEAN;
        }

        // Default to string
        return ExportValue::TYPE_STRING;
    }

    /**
     * Populate table from grouping data
     *
     * @param ExportTable $table
     * @param array $values Two-dimensional array with rows and values
     * @param array $names Column names
     * @param array $hidden Hidden column indices/names
     * @param array $options
     */
    private function populateTableFromGroupingData(ExportTable $table, array $values, array $names, array $hidden, array $options): void
    {
        foreach ($values as $rowData) {
            $record = $this->createRecordFromRowData($rowData, $names, $hidden, $options);
            if ($record) {
                $table->addRecord($record, false);
            }
        }
    }

    /**
     * Create export record from row data
     *
     * @param array $rowData
     * @param array $names
     * @param array $hidden
     * @param array $options
     * @return ExportRecord|null
     */
    private function createRecordFromRowData(array $rowData, array $names, array $hidden, array $options): ?ExportRecord
    {
        $record = new ExportRecord();

        foreach ($names as $index => $varName) {
            // Skip hidden columns
            if (in_array($varName, $hidden) || in_array($index, $hidden)) {
                continue;
            }

            // Get value by index, handle missing values
            $value = $rowData[$index] ?? null;
            $type = $this->guessColumnType($varName);
            $formattedValue = $this->formatValue($value, $type, null, $options);

            $record->addValue($varName, $formattedValue, $type);
        }

        return $record;
    }



    /**
     * Format value based on type
     *
     * @param mixed $value
     * @param string $type
     * @param string|null $format
     * @param array $options
     * @return mixed
     */
    private function formatValue($value, string $type, ?string $format, array $options)
    {
        if ($value === null) {
            return null;
        }

        switch ($type) {
            case ExportValue::TYPE_DATE:
                if ($value instanceof \DateTimeInterface) {
                    return $value;
                } elseif (is_string($value) && strtotime($value) !== false) {
                    return new \DateTime($value);
                }
                break;

            case ExportValue::TYPE_DATETIME:
                if ($value instanceof \DateTimeInterface) {
                    return $value;
                } elseif (is_string($value) && strtotime($value) !== false) {
                    return new \DateTime($value);
                }
                break;

            case ExportValue::TYPE_INTEGER:
                return (int) $value;

            case ExportValue::TYPE_FLOAT:
                return (float) $value;

            case ExportValue::TYPE_BOOLEAN:
                return (bool) $value;
        }

        return $value;
    }

    /**
     * {@inheritdoc}
     */
    public function getSupportedTableTypes(): array
    {
        // Since we auto-discover tables, we can't know supported types in advance
        // Return empty array or implement caching if needed
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public function supportsTableType(string $tableType): bool
    {
        // Since we auto-discover tables, we support any table type that exists in the model
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function getTableConfiguration(string $tableType): array
    {
        // Return basic configuration since we auto-discover structure
        return [
            'name' => $this->formatTableName($tableType),
            'auto_discovered' => true
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function validateRecord($record, array $requestedTableTypes = []): bool
    {
        if (!($record instanceof \Model)) {
            return false;
        }

        // Since we auto-discover tables, we can always try to extract them
        // The validation is done during extraction
        return true;
    }
}
